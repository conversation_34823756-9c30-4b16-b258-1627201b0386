import { useMutation, useQueryClient } from '@tanstack/react-query'
import { createBox } from '@/services/Boxes'
import { BoxDetails } from '@/@types/box'
import { showSuccessToast } from '@/utils/globalErrorHandler'
import { useError<PERSON>andler } from '@/utils/errorHandler400'
import { AxiosError } from 'axios'

export const useCreateBox = () => {
    const queryClient = useQueryClient()
    const { handle400Errors } = useErrorHandler()

    return useMutation<
        BoxDetails,
        AxiosError,
        { organizationalNodeId: number | string }
    >({
        mutationFn: ({ organizationalNodeId }) =>
            createBox(organizationalNodeId),
        onSuccess: (data) => {
            // Invalidate boxes queries to refetch updated data
            queryClient.invalidateQueries({ queryKey: ['boxes'] })
            // Optionally set the new box data in cache
            queryClient.setQueryData(['box', data.boxId], data)
            showSuccessToast('Box created successfully!')
        },
        onError: (error: AxiosError) => {
            handle400Errors(error, 'Failed to create box')
        },
    })
}
