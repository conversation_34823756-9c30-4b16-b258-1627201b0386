import { useMutation, useQueryClient } from '@tanstack/react-query'
import { closeBox } from '@/services/Boxes'
import { showSuccessToast } from '@/utils/globalErrorHandler'
import { useErrorHandler } from '@/utils/errorHandler400'
import { AxiosError } from 'axios'

export const useCloseBox = () => {
    const queryClient = useQueryClient()
    const { handle400Errors } = useErrorHandler()

    return useMutation<void, AxiosError, { boxId: string | number }>({
        mutationFn: ({ boxId }) => closeBox(boxId),
        onSuccess: (_, variables) => {
            // Invalidate boxes queries to refetch updated data
            queryClient.invalidateQueries({ queryKey: ['boxes'] })
            // Invalidate the specific box query to refetch updated box details
            queryClient.invalidateQueries({
                queryKey: ['box', variables.boxId],
            })
            showSuccessToast('Box closed successfully!')
        },
        onError: (error: AxiosError) => {
            handle400Errors(error, 'Failed to close box')
        },
    })
}
