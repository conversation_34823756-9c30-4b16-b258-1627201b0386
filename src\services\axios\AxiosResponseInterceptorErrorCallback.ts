import { tokenStorage } from '@/utils/tokenStorage'
import type { AxiosError } from 'axios'
import API from './AxiosBase'
import { useAuthStore } from '@/views/auth/store/Auth'
import { handleGlobalError } from '@/utils/globalErrorHandler'

const unauthorizedCode = [401, 419, 440]
let isRefreshing = false

const AxiosResponseInterceptorErrorCallback = async (error: AxiosError) => {
    const { response, config } = error

    if (
        response &&
        unauthorizedCode.includes(response.status) &&
        !isRefreshing
    ) {
        isRefreshing = true

        try {
            const token = await tokenStorage.getToken()
            const refreshToken = await tokenStorage.getRefreshToken()

            if (token && refreshToken) {
                const refreshResponse = await API.post('/Auth/refresh-token', {
                    token,
                    refreshToken,
                })
                console.log('refreshResponse', refreshResponse)

                if (
                    refreshResponse.data.token &&
                    refreshResponse.data.refreshToken
                ) {
                    tokenStorage.setToken(refreshResponse.data.token)
                    tokenStorage.setRefreshToken(
                        refreshResponse.data.refreshToken,
                    )

                    isRefreshing = false
                    // Retry the original request
                    return API(config!)
                }
            }
        } catch (refreshError) {
            console.error('Token refresh failed:', refreshError)
        }

        isRefreshing = false

        // Call logout directly from the store since this is not a React component
        useAuthStore.getState().logout()
    }

    // Handle global errors (show toast for non-400 errors)
    handleGlobalError(error)

    return Promise.reject(error?.response?.data || error)
}

export default AxiosResponseInterceptorErrorCallback
