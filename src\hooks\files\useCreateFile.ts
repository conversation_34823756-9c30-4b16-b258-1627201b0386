import { useMutation, useQueryClient } from '@tanstack/react-query'
import { createFile } from '@/services/FilesService'
import { showSuccessToast } from '@/utils/globalErrorHandler'
import { useErrorHandler } from '@/utils/errorHandler400'
import { AxiosError } from 'axios'

export const useCreateFile = () => {
    const queryClient = useQueryClient()
    const { handle400Errors } = useErrorHandler()

    return useMutation<any, AxiosError, any>({
        mutationFn: createFile,
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: ['files'] })
            showSuccessToast('File created successfully!')
        },
        onError: (error: AxiosError) => {
            handle400Errors(error, 'Failed to create file')
        },
    })
}
