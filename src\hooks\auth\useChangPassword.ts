import { useMutation } from '@tanstack/react-query'
import { changePasswordApi } from '@/services/AuthService'
import { showSuccessToast } from '@/utils/globalErrorHandler'
import { useErrorHandler } from '@/utils/errorHandler400'
import { AxiosError } from 'axios'

interface ChangePasswordData {
    currentPassword: string
    newPassword: string
}

export function useChangePassword() {
    const { handle400Errors } = useErrorHandler()

    return useMutation<void, AxiosError, ChangePasswordData>({
        mutationFn: (data: ChangePasswordData) => changePasswordApi(data),
        onSuccess: () => {
            showSuccessToast('Password changed successfully!')
        },
        onError: (error: AxiosError) => {
            handle400Errors(error, 'Failed to change password')
        },
    })
}
