import { useMutation } from '@tanstack/react-query'
import { signInApi } from '@/services/AuthService'
import { useAuthStore } from '@/views/auth/store/Auth'
import { useErrorHandler } from '@/utils/errorHandler400'
import { AxiosError } from 'axios'

export function useSignIn() {
    const { setAuth } = useAuthStore()
    const { handle400Errors } = useErrorHandler()

    return useMutation({
        mutationFn: signInApi,
        onSuccess: (data) => {
            if (data && data.token) {
                setAuth(data.token, data.refreshToken)
            }
        },
        onError: (error: AxiosError) => {
            handle400Errors(error, 'Authentication failed. Please try again.')
        },
    })
}
