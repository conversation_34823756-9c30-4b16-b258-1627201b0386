import { ErrorResponse } from '@/@types/global'
import { AxiosError } from 'axios'
import { toast, Notification } from '@/components/ui'
import { useTranslation } from 'react-i18next'

export const useErrorHandler = () => {
    const { t } = useTranslation()

    const handle400Errors = (
        error: AxiosError,
        fallbackMessage: string = 'An error occurred',
    ) => {
        if (error.response?.status !== 400) {
            return false // Not a 400 error
        }

        const errors: ErrorResponse[] =
            (error.response?.data as { errors?: ErrorResponse[] })?.errors || []

        if (errors.length === 0) {
            showErrorToast(fallbackMessage)
            return true
        }

        // Handle each error in the array
        errors.forEach((errorItem) => {
            const translatedMessage = getTranslatedMessage(
                errorItem.code,
                errorItem.description,
            )
            showErrorToast(translatedMessage)
        })

        return true
    }

    const getTranslatedMessage = (
        code: string,
        description: string,
    ): string => {
        const generalErrorKey = `errors.${code}`
        const generalTranslation = t(generalErrorKey)

        if (generalTranslation !== generalErrorKey) {
            return generalTranslation
        }

        // Fall back to server description
        return description || 'Unknown error occurred'
    }

    const showErrorToast = (message: string) => {
        toast.push(
            <Notification title="" type="danger" duration={3000}>
                {message}
            </Notification>,
            { placement: 'top-center' },
        )
    }

    return { handle400Errors }
}
