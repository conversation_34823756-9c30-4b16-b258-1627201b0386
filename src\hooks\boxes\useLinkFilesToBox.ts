import { useMutation, useQueryClient } from '@tanstack/react-query'
import { linkFilesToBox } from '@/services/Boxes'
import { showSuccessToast } from '@/utils/globalErrorHandler'
import { useErrorHandler } from '@/utils/errorHandler400'
import { AxiosError } from 'axios'

export const useLinkFilesToBox = () => {
    const queryClient = useQueryClient()
    const { handle400Errors } = useErrorHandler()

    return useMutation<
        void,
        AxiosError,
        { boxId: string | number; fileIds: string[] }
    >({
        mutationFn: ({ boxId, fileIds }) => linkFilesToBox(boxId, fileIds),
        onSuccess: (_, variables) => {
            // Invalidate boxes queries to refetch updated data
            queryClient.invalidateQueries({ queryKey: ['boxes'] })
            // Invalidate the specific box query to refetch updated box details
            queryClient.invalidateQueries({
                queryKey: ['box', variables.boxId],
            })
            // Optionally invalidate files queries if they exist
            queryClient.invalidateQueries({ queryKey: ['files'] })
            showSuccessToast('Files linked to box successfully!')
        },
        onError: (error: AxiosError) => {
            handle400Errors(error, 'Failed to link files to box')
        },
    })
}
