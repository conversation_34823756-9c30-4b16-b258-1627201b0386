/* eslint-disable @typescript-eslint/no-explicit-any */
/* eslint-disable @typescript-eslint/no-unused-vars */
import { useState } from 'react'
import Dialog from '@/components/ui/Dialog'
import Button from '@/components/ui/Button'
import UserForm from './UserForm'
import { CreateUser, User } from '@/@types/auth'
import Notification from '@/components/ui/Notification'
import toast from '@/components/ui/toast'
import { t } from 'i18next'
import { useCreateUser, useUpdateUser, useUpdateUserStatus } from '@/hooks/user'

type UserModelProps = {
    dialogIsOpen: boolean
    onDialogClose: () => void
    user?: User | null
}

const UserModel = (props: UserModelProps) => {
    const { dialogIsOpen, onDialogClose, user } = props
    const {
        mutateAsync: createUser,
        isPending: createUserIsPending,
        isSuccess: createUserIsSuccess,
    } = useCreateUser()
    const {
        mutateAsync: updateUser,
        isPending: updateUserIsPending,
        isSuccess: updateUserIsSuccess,
    } = useUpdateUser()
    const {
        mutateAsync: updateUserStatus,
        isPending: updateUserStatusIsPending,
    } = useUpdateUserStatus()

    const handleFormSubmit = async (values: CreateUser) => {
        if (user) {
            await updateUser({ id: user.id, userData: values })
        } else {
            await createUser({ data: values })
        }
        if (createUserIsSuccess || updateUserIsSuccess) onDialogClose()
    }

    return (
        <Dialog
            isOpen={dialogIsOpen}
            shouldCloseOnOverlayClick={false}
            shouldCloseOnEsc={
                !createUserIsPending &&
                !updateUserIsPending &&
                !updateUserStatusIsPending
            }
            width={750}
            height={500}
            className={''}
            onClose={onDialogClose}
            onRequestClose={onDialogClose}
        >
            <div className="flex flex-col h-full justify-between ">
                <h4 className="text-xl font-semibold pb-2 border-b border-gray-200">
                    {user
                        ? t('nav.systemSecurity.editUser')
                        : t('nav.systemSecurity.addUser')}
                </h4>
                <div className="overflow-y-auto">
                    <UserForm
                        loading={
                            createUserIsPending ||
                            updateUserIsPending ||
                            updateUserStatusIsPending
                        }
                        newUser={!user}
                        defaultValues={
                            user
                                ? {
                                      firstName: user.firstName || '',
                                      lastName: user.lastName || '',
                                      email: user.email || '',
                                      phoneNumber: user.phoneNumber || '',
                                      organizationalNodeCodes:
                                          user.organizationalNodes.map(
                                              (node) => node.code,
                                          ),
                                  }
                                : undefined
                        }
                        onFormSubmit={handleFormSubmit}
                    >
                        <div className="flex items-center justify-end gap-2">
                            <Button
                                size="sm"
                                type="button"
                                disabled={
                                    createUserIsPending ||
                                    updateUserIsPending ||
                                    updateUserStatusIsPending
                                }
                                onClick={onDialogClose}
                            >
                                {t('nav.shared.cancel')}
                            </Button>
                            {user && (
                                <Button
                                    size="sm"
                                    type="button"
                                    disabled={
                                        createUserIsPending ||
                                        updateUserIsPending ||
                                        updateUserStatusIsPending
                                    }
                                    variant="solid"
                                    className={
                                        user?.status === 1
                                            ? 'bg-blue-500 hover:bg-blue-600 border-blue-500 hover:border-blue-600'
                                            : 'bg-emerald-500 hover:bg-emerald-600 border-emerald-500 hover:border-emerald-600'
                                    }
                                    onClick={async () => {
                                        if (user) {
                                            await updateUserStatus(
                                                {
                                                    userId: user.id,
                                                    status:
                                                        user.status === 1
                                                            ? 2
                                                            : 1,
                                                },
                                                {
                                                    onSuccess: () => {
                                                        onDialogClose()
                                                    },
                                                },
                                            )
                                        }
                                    }}
                                >
                                    {user?.status === 1
                                        ? t('nav.GlobalActions.frozen')
                                        : t('nav.GlobalActions.approve')}
                                </Button>
                            )}
                            <Button
                                size="sm"
                                variant="solid"
                                type="submit"
                                loading={
                                    createUserIsPending ||
                                    updateUserIsPending ||
                                    updateUserStatusIsPending
                                }
                            >
                                {user
                                    ? t('nav.shared.update')
                                    : t('nav.shared.create')}
                            </Button>
                        </div>
                    </UserForm>
                </div>
            </div>
        </Dialog>
    )
}

export default UserModel
