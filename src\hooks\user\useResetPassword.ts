import { resetUserPasswordApi } from '@/services/UsersService'
import { useMutation } from '@tanstack/react-query'
import { showSuccessToast } from '@/utils/globalErrorHandler'
import { useErrorHandler } from '@/utils/errorHandler400'
import { AxiosError } from 'axios'

export const useResetPassword = () => {
    const { handle400Errors } = useErrorHandler()

    return useMutation<void, AxiosError, { userId: string }>({
        mutationFn: ({ userId }) => resetUserPasswordApi(userId),
        onSuccess: () => {
            showSuccessToast('Password reset successfully!')
        },
        onError: (error: AxiosError) => {
            handle400Errors(error, 'Failed to reset password')
        },
    })
}
