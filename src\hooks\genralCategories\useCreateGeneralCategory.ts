import { useMutation, useQueryClient } from '@tanstack/react-query'
import { createGeneralCategory } from '@/services/GeneralCategoriesService'
import { showSuccessToast } from '@/utils/globalErrorHandler'
import { useErrorHandler } from '@/utils/errorHandler400'
import { AxiosError } from 'axios'

export const useCreateGeneralCategory = () => {
    const queryClient = useQueryClient()
    const { handle400Errors } = useErrorHandler()

    return useMutation<any, AxiosError, string>({
        mutationFn: createGeneralCategory,
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: ['generalCategories'] })
            showSuccessToast('General category created successfully!')
        },
        onError: (error: AxiosError) => {
            handle400Errors(error, 'Failed to create general category')
        },
    })
}
