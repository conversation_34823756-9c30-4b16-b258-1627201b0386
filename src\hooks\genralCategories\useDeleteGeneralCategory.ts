import { useMutation, useQueryClient } from '@tanstack/react-query'
import { deleteGeneralCategory } from '@/services/GeneralCategoriesService'
import { showSuccessToast } from '@/utils/globalErrorHandler'
import { useErrorHandler } from '@/utils/errorHandler400'
import { AxiosError } from 'axios'

export const useDeleteGeneralCategory = () => {
    const queryClient = useQueryClient()
    const { handle400Errors } = useErrorHandler()

    return useMutation<any, AxiosError, number>({
        mutationFn: deleteGeneralCategory,
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: ['generalCategories'] })
            showSuccessToast('General category deleted successfully!')
        },
        onError: (error: AxiosError) => {
            handle400Errors(error, 'Failed to delete general category')
        },
    })
}
