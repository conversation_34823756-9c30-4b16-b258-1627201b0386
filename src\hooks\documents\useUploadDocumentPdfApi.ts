import { useMutation, useQueryClient } from '@tanstack/react-query'
import { uploadDocumentPdfApi } from '@/services/DocumentsService'
import { showSuccessToast } from '@/utils/globalErrorHandler'
import { useErrorHandler } from '@/utils/errorHandler400'
import { AxiosError } from 'axios'

export const useUploadDocumentPdf = () => {
    const queryClient = useQueryClient()
    const { handle400Errors } = useErrorHandler()

    return useMutation({
        mutationFn: ({
            documentId,
            pdfFile,
        }: {
            documentId: string
            pdfFile: File
        }) => uploadDocumentPdfApi(documentId, pdfFile),
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: ['documents'] })
            showSuccessToast('Document PDF uploaded successfully!')
        },
        onError: (error: AxiosError) => {
            handle400Errors(error, 'Failed to upload document PDF')
        },
    })
}
