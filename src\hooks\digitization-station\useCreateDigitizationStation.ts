/* eslint-disable @typescript-eslint/no-explicit-any */
import { useMutation, useQueryClient } from '@tanstack/react-query'
import { createDigitizationStation } from '@/services/DigitizationStationService'
import type { DigitizationStationRequest } from '@/@types/digitizationStation'
import { showSuccessToast } from '@/utils/globalErrorHandler'
import { useErrorHandler } from '@/utils/errorHandler400'
import { AxiosError } from 'axios'

export const useCreateDigitizationStation = () => {
    const queryClient = useQueryClient()
    const { handle400Errors } = useErrorHandler()

    return useMutation<any, AxiosError, DigitizationStationRequest>({
        mutationFn: createDigitizationStation,
        onSuccess: () => {
            queryClient.invalidateQueries({
                queryKey: ['digitization-stations'],
            })
            showSuccessToast('Digitization station created successfully!')
        },
        onError: (error: AxiosError) => {
            handle400Errors(error, 'Failed to create digitization station')
        },
    })
}
