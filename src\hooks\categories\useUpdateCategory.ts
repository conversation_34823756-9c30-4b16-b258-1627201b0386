/* eslint-disable @typescript-eslint/no-explicit-any */
import { useMutation, useQueryClient } from '@tanstack/react-query'
import { updateCategory } from '@/services/CategoriesService'
import type { Category } from '@/@types/categories'
import { showSuccessToast } from '@/utils/globalErrorHandler'
import { useErrorHandler } from '@/utils/errorHandler400'
import { AxiosError } from 'axios'

export const useUpdateCategory = () => {
    const queryClient = useQueryClient()
    const { handle400Errors } = useErrorHandler()

    return useMutation<
        any,
        AxiosError,
        { id: string | number; category: Partial<Category> }
    >({
        mutationFn: ({ id, category }) => updateCategory(id, category),
        onSuccess: (_, { id }) => {
            queryClient.invalidateQueries({ queryKey: ['categories'] })
            queryClient.invalidateQueries({ queryKey: ['category', id] })
            showSuccessToast('Category updated successfully!')
        },
        onError: (error: AxiosError) => {
            handle400Errors(error, 'Failed to update category')
        },
    })
}
