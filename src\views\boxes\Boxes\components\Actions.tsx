import { useState } from 'react'
import { useNavigate } from 'react-router-dom'
import useTranslation from '@/utils/hooks/useTranslation'
import { Box } from '@/@types/box'
import { Tooltip } from '@/components/ui'
import { Tb<PERSON>ockBolt, TbPrinter, TbTrash, TbEye } from 'react-icons/tb'
import PrintBarCode from '@/components/shared/displaying/PrintBarCode'
import { ConfirmDialog } from '@/components/shared'
import { useDeleteBox } from '@/hooks/boxes/useDeleteBox'

export default function Actions({
    boxData,
}: {
    boxData: Box
    boxView?: boolean
}) {
    const { t } = useTranslation()
    const navigate = useNavigate()

    const [print, setPrint] = useState(false)
    const [deleteConfirmation, setDeleteConfirmation] = useState(false)

    const { mutate: deleteBox, isPending: isDeleting } = useDeleteBox()

    const handleCancelDelete = () => {
        setDeleteConfirmation(false)
    }

    const handleConfirmDelete = () => {
        deleteBox(
            { boxId: boxData.boxId },
            {
                onSuccess: () => {
                    setDeleteConfirmation(false)
                },
            },
        )
    }

    const onClose = () => {
        console.log(boxData.numberOfFiles)
    }

    const handleViewFiles = () => {
        navigate(`/boxes/${boxData.boxId}/files`)
    }

    return (
        <div className="flex items-center justify-center gap-3">
            {boxData.numberOfFiles > 0 && (
                <Tooltip title={t('nav.boxes.viewFiles')}>
                    <div
                        aria-label={t('nav.boxes.viewFiles')}
                        className="text-blue-600 hover:text-blue-700 hover:bg-blue-50 rounded-lg transition-colors duration-200 cursor-pointer"
                        role="button"
                        onClick={handleViewFiles}
                    >
                        <TbEye className="text-lg" />
                    </div>
                </Tooltip>
            )}

            {boxData.boxStatus === 0 && boxData?.numberOfFiles > 0 && (
                <Tooltip title={t('nav.shared.close')}>
                    <div
                        aria-label={t('nav.shared.close')}
                        className="text-green-600 hover:text-green-700 hover:bg-green-50 rounded-lg transition-colors duration-200 cursor-pointer"
                        role="button"
                        onClick={onClose}
                    >
                        <TbLockBolt className="text-lg" />
                    </div>
                </Tooltip>
            )}

            <Tooltip title={t('nav.shared.print')}>
                <div
                    aria-label={t('nav.shared.print')}
                    className="text-green-600 hover:text-green-700 hover:bg-green-50 rounded-lg transition-colors duration-200 cursor-pointer"
                    role="button"
                    onClick={() => {
                        setPrint(true)
                    }}
                >
                    <TbPrinter className="text-lg" />
                </div>
            </Tooltip>

            {boxData.boxStatus === 0 && (
                <Tooltip title={t('nav.shared.delete')}>
                    <div
                        aria-label={t('nav.shared.delete')}
                        className={`text-red-600 hover:text-red-700 hover:bg-red-50 rounded-lg transition-colors duration-200 cursor-pointer ${isDeleting ? 'opacity-50 cursor-not-allowed' : ''}`}
                        role="button"
                        onClick={() =>
                            !isDeleting && setDeleteConfirmation(true)
                        }
                    >
                        <TbTrash className="text-lg" />
                    </div>
                </Tooltip>
            )}

            {print && (
                <PrintBarCode
                    fileId={boxData.boxId}
                    onClose={() => setPrint(false)}
                />
            )}

            <ConfirmDialog
                isOpen={deleteConfirmation}
                type="danger"
                title={t('nav.shared.deleteConfirmation')}
                confirmText={t('nav.shared.delete')}
                cancelText={t('nav.shared.cancel')}
                confirmButtonProps={{ loading: isDeleting }}
                onClose={handleCancelDelete}
                onRequestClose={handleCancelDelete}
                onCancel={handleCancelDelete}
                onConfirm={handleConfirmDelete}
            >
                <p>
                    {t('nav.shared.deleteConfirmationMessage', {
                        item: boxData.boxId || 'box',
                    })}
                </p>
            </ConfirmDialog>
        </div>
    )
}
