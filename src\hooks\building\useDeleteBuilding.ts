import { useMutation, useQueryClient } from '@tanstack/react-query'
import { deleteBuilding } from '@/services/BuildingService'
import { showSuccessToast } from '@/utils/globalErrorHandler'
import { useErrorHandler } from '@/utils/errorHandler400'
import { AxiosError } from 'axios'

export const useDeleteBuilding = () => {
    const queryClient = useQueryClient()
    const { handle400Errors } = useErrorHandler()

    return useMutation<any, AxiosError, number>({
        mutationFn: (id: number) => deleteBuilding(id),
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: ['buildings'] })
            showSuccessToast('Building deleted successfully!')
        },
        onError: (error: AxiosError) => {
            handle400Errors(error, 'Failed to delete building')
        },
    })
}
