import { ErrorResponse } from '@/@types/global'
import { AxiosError } from 'axios'

/**
 * Extract errors array from 400 status responses
 * @param error - Axios error object
 * @returns Array of ErrorResponse objects or null if not a 400 error
 */
export const checkErrors = (error: AxiosError): ErrorResponse[] | null => {
    if (error.response?.status === 400) {
        const errors: ErrorResponse[] =
            (error.response?.data as { errors?: ErrorResponse[] })?.errors || []
        return errors
    }
    return null
}

/**
 * Check if an error is a 400 status error
 * @param error - Axios error object
 * @returns boolean indicating if it's a 400 error
 */
export const is400Error = (error: AxiosError): boolean => {
    return error.response?.status === 400
}

/**
 * Extract error data from any Axios error response
 * @param error - Axios error object
 * @returns The error data object or null
 */
export const getErrorData = (error: AxiosError): unknown => {
    return error.response?.data || null
}
