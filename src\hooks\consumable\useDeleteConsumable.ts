import { useMutation, useQueryClient } from '@tanstack/react-query'
import { deleteConsumable } from '@/services/ConsumableService'
import { showSuccessToast } from '@/utils/globalErrorHandler'
import { useErrorHandler } from '@/utils/errorHandler400'
import { AxiosError } from 'axios'

export const useDeleteConsumable = () => {
    const queryClient = useQueryClient()
    const { handle400Errors } = useErrorHandler()

    return useMutation<any, AxiosError, number>({
        mutationFn: (id: number) => deleteConsumable(id),
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: ['consumables'] })
            showSuccessToast('Consumable deleted successfully!')
        },
        onError: (error: AxiosError) => {
            handle400Errors(error, 'Failed to delete consumable')
        },
    })
}
