import { useMutation, useQueryClient } from '@tanstack/react-query'
import type { CreateUser } from '@/@types/auth'
import { updateUserApi } from '@/services/UsersService'
import { showSuccessToast } from '@/utils/globalErrorHandler'
import { useErrorHandler } from '@/utils/errorHandler400'
import { AxiosError } from 'axios'

export const useUpdateUser = () => {
    const queryClient = useQueryClient()
    const { handle400Errors } = useErrorHandler()

    return useMutation<void, AxiosError, { id: string; userData: CreateUser }>({
        mutationFn: ({ id, userData }) => updateUserApi(id, userData),
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: ['users'] })
            showSuccessToast('User updated successfully!')
        },
        onError: (error: AxiosError) => {
            handle400Errors(error, 'Failed to update user')
        },
    })
}
