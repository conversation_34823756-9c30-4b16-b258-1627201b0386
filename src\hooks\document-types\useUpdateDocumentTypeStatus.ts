import { useMutation, useQueryClient } from '@tanstack/react-query'
import { updateDocumentTypeStatus } from '@/services/DocumentTypesService'
import { showSuccessToast } from '@/utils/globalErrorHandler'
import { useError<PERSON>and<PERSON> } from '@/utils/errorHandler400'
import { AxiosError } from 'axios'

export const useUpdateDocumentTypeStatus = () => {
    const queryClient = useQueryClient()
    const { handle400Errors } = useErrorHandler()

    return useMutation<any, AxiosError, { id: number; status: number }>({
        mutationFn: ({ id, status }: { id: number; status: number }) =>
            updateDocumentTypeStatus(id, status),
        onSuccess: (_, { id }) => {
            queryClient.invalidateQueries({ queryKey: ['document-types'] })
            queryClient.invalidateQueries({ queryKey: ['document-type', id] })
            showSuccessToast('Document type status updated successfully!')
        },
        onError: (error: AxiosError) => {
            handle400Errors(error, 'Failed to update document type status')
        },
    })
}
