import { deleteUserApi } from '@/services/UsersService'
import { showSuccessToast } from '@/utils/globalErrorHandler'
import { useMutation, useQueryClient } from '@tanstack/react-query'
import { useErrorHandler } from '@/utils/errorHandler400'
import { AxiosError } from 'axios'

export const useDeleteUser = () => {
    const queryClient = useQueryClient()
    const { handle400Errors } = useErrorHandler()

    return useMutation<void, AxiosError, string>({
        mutationFn: (userId: string) => deleteUserApi(userId),
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: ['users'] })
            showSuccessToast('User deleted successfully!')
        },
        onError: (error) => {
            handle400Errors(error, 'Failed to delete user')
        },
    })
}
