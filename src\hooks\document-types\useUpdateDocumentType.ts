import { useMutation, useQueryClient } from '@tanstack/react-query'
import { updateDocumentType } from '@/services/DocumentTypesService'
import type { DocumentType } from '@/services/DocumentTypesService'
import { showSuccessToast } from '@/utils/globalErrorHandler'
import { useErrorHandler } from '@/utils/errorHandler400'
import { AxiosError } from 'axios'

export const useUpdateDocumentType = () => {
    const queryClient = useQueryClient()
    const { handle400Errors } = useErrorHandler()

    return useMutation<
        any,
        AxiosError,
        { id: number; documentType: DocumentType }
    >({
        mutationFn: ({
            id,
            documentType,
        }: {
            id: number
            documentType: DocumentType
        }) => updateDocumentType(id, documentType),
        onSuccess: (_, { id }) => {
            queryClient.invalidateQueries({ queryKey: ['document-types'] })
            queryClient.invalidateQueries({ queryKey: ['document-type', id] })
            showSuccessToast('Document type updated successfully!')
        },
        onError: (error: AxiosError) => {
            handle400Errors(error, 'Failed to update document type')
        },
    })
}
