import { useMutation, useQueryClient } from '@tanstack/react-query'
import { markDocumentAsDigitizedApi } from '@/services/DocumentsService'
import { AxiosError } from 'axios'

import { showSuccessToast } from '@/utils/globalErrorHandler'
import { useErrorHandler } from '@/utils/errorHandler400'

export const useMarkDocumentAsDigitized = () => {
    const queryClient = useQueryClient()
    const { handle400Errors } = useErrorHandler()

    return useMutation({
        mutationFn: ({ documentId }: { documentId: string }) =>
            markDocumentAsDigitizedApi(documentId),
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: ['documents'] })
            showSuccessToast('Document marked as digitized successfully!')
        },
        onError: (error: AxiosError) => {
            handle400Errors(error, 'Failed to mark document as digitized')
        },
    })
}
