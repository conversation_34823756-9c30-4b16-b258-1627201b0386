import { Status } from '@/@types/common'
import { updateUserStatusApi } from '@/services/UsersService'
import { useMutation, useQueryClient } from '@tanstack/react-query'
import { showSuccessToast } from '@/utils/globalErrorHandler'
import { useErrorHandler } from '@/utils/errorHandler400'
import { AxiosError } from 'axios'

export const useUpdateUserStatus = () => {
    const queryClient = useQueryClient()
    const { handle400Errors } = useErrorHandler()

    return useMutation<void, AxiosError, { userId: string; status: Status }>({
        mutationFn: ({ userId, status }) => updateUserStatusApi(userId, status),
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: ['users'] })
            showSuccessToast('User status updated successfully!')
        },
        onError: (error: AxiosError) => {
            handle400Errors(error, 'Failed to update user status')
        },
    })
}
