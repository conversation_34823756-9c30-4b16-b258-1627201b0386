import { useMutation, useQueryClient } from '@tanstack/react-query'
import { deleteBox } from '@/services/Boxes'
import { showSuccessToast } from '@/utils/globalErrorHandler'
import { useErrorHandler } from '@/utils/errorHandler400'
import { AxiosError } from 'axios'

export const useDeleteBox = () => {
    const queryClient = useQueryClient()
    const { handle400Errors } = useErrorHandler()

    return useMutation<void, AxiosError, { boxId: string | number }>({
        mutationFn: ({ boxId }) => deleteBox(boxId),
        onSuccess: () => {
            // Invalidate boxes queries to refetch updated data
            queryClient.invalidateQueries({ queryKey: ['boxes'] })
            showSuccessToast('Box deleted successfully!')
        },
        onError: (error: AxiosError) => {
            handle400Errors(error, 'Failed to delete box')
        },
    })
}
