import { useMutation, useQueryClient } from '@tanstack/react-query'
import { uploadDocumentAttachmentsApi } from '@/services/DocumentsService'

import { showSuccessToast } from '@/utils/globalErrorHandler'
import { useErrorHandler } from '@/utils/errorHandler400'
import { AxiosError } from 'axios'

export const useUploadDocumentAttachments = () => {
    const queryClient = useQueryClient()
    const { handle400Errors } = useErrorHandler()

    return useMutation({
        mutationFn: ({
            documentId,
            attachments,
        }: {
            documentId: string
            attachments: File[]
        }) => uploadDocumentAttachmentsApi(documentId, attachments),
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: ['documents'] })
            showSuccessToast('Document attachments uploaded successfully!')
        },
        onError: (error: AxiosError) => {
            handle400Errors(error, 'Failed to upload document attachments')
        },
    })
}
