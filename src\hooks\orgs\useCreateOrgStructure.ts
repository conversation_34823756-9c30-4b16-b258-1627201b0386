import { createOrganizeStructure } from '@/services/OrganizeStructureService'
import { useMutation, useQueryClient } from '@tanstack/react-query'
import type { CreatedNode } from '@/@types/organizationalStructure'
import { showSuccessToast } from '@/utils/globalErrorHandler'
import { useErrorHandler } from '@/utils/errorHandler400'
import { AxiosError } from 'axios'

export const useCreateOrgStructure = () => {
    const queryClient = useQueryClient()
    const { handle400Errors } = useErrorHandler()

    return useMutation<any, AxiosError, CreatedNode>({
        mutationFn: (data: CreatedNode) => createOrganizeStructure(data),
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: ['orgsStructure'] })
            showSuccessToast('Organization structure created successfully!')
        },
        onError: (error: AxiosError) => {
            handle400Errors(error, 'Failed to create organization structure')
        },
    })
}
