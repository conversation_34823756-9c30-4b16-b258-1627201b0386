import { useMutation, useQueryClient } from '@tanstack/react-query'
import { updateStockStatus } from '@/services/StockService'
import type { Status } from '@/@types/common'
import { showSuccessToast } from '@/utils/globalErrorHandler'
import { useErrorHandler } from '@/utils/errorHandler400'
import { AxiosError } from 'axios'

export const useUpdateStockStatus = () => {
    const queryClient = useQueryClient()
    const { handle400Errors } = useErrorHandler()

    return useMutation<void, AxiosError, { id: number; newStatus: Status }>({
        mutationFn: ({ id, newStatus }) => updateStockStatus(id, newStatus),
        onSuccess: (_, { id }) => {
            queryClient.invalidateQueries({ queryKey: ['stocks'] })
            queryClient.invalidateQueries({ queryKey: ['stock', id] })
            showSuccessToast('Stock status updated successfully!')
        },
        onError: (error: AxiosError) => {
            handle400Errors(error, 'Failed to update stock status')
        },
    })
}
