/* eslint-disable @typescript-eslint/no-explicit-any */
import { useMutation, useQueryClient } from '@tanstack/react-query'
import { updateWarehouseStatus } from '@/services/Warehouses'
import type { Status } from '@/@types/common'
import { showSuccessToast } from '@/utils/globalErrorHandler'
import { useErrorHandler } from '@/utils/errorHandler400'
import { AxiosError } from 'axios'

export const useUpdateWarehouseStatus = () => {
    const queryClient = useQueryClient()
    const { handle400Errors } = useErrorHandler()

    return useMutation<
        any,
        AxiosError,
        { id: string; statusUpdate: { newStatus: Status } }
    >({
        mutationFn: ({ id, statusUpdate }) =>
            updateWarehouseStatus(id, statusUpdate),
        onSuccess: (_, { id }) => {
            queryClient.invalidateQueries({ queryKey: ['warehouses'] })
            queryClient.invalidateQueries({ queryKey: ['warehouse', id] })
            showSuccessToast('Warehouse status updated successfully!')
        },
        onError: (error: AxiosError) => {
            handle400Errors(error, 'Failed to update warehouse status')
        },
    })
}
