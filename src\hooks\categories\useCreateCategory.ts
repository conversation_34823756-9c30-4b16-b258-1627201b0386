/* eslint-disable @typescript-eslint/no-explicit-any */
import { useMutation, useQueryClient } from '@tanstack/react-query'
import { createCategory } from '@/services/CategoriesService'
import type { Category } from '@/@types/categories'
import { showSuccessToast } from '@/utils/globalErrorHandler'
import { useErrorHandler } from '@/utils/errorHandler400'
import { AxiosError } from 'axios'

export const useCreateCategory = () => {
    const queryClient = useQueryClient()
    const { handle400Errors } = useErrorHandler()

    return useMutation<any, AxiosError, Omit<Category, 'id'>>({
        mutationFn: createCategory,
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: ['categories'] })
            showSuccessToast('Category created successfully!')
        },
        onError: (error: AxiosError) => {
            handle400Errors(error, 'Failed to create category')
        },
    })
}
