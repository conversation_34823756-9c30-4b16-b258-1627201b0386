import React, { useState } from 'react'
import { <PERSON>lt<PERSON> } from '@/components/ui'
import { HiOutlinePencil, HiOutlineTrash } from 'react-icons/hi'
import { PiKeyholeDuotone } from 'react-icons/pi'
import ConfirmDialog from '@/components/shared/ConfirmDialog'
import useTranslation from '@/utils/hooks/useTranslation'
import { User } from '@/@types/auth'
import { useDeleteUser, useResetPassword } from '@/hooks/user'

interface ActionsProps {
    userData: User
    onEdit?: (user: User) => void
}

export default function Actions({ userData, onEdit }: ActionsProps) {
    const { t } = useTranslation()
    const { mutateAsync: resetPassword } = useResetPassword()
    const { mutateAsync: deleteUser } = useDeleteUser()

    const [resetPasswordConfirmationOpen, setResetPasswordConfirmationOpen] =
        useState(false)
    const [deleteConfirmationOpen, setDeleteConfirmationOpen] = useState(false)

    // Handle reset password confirmation
    const handleResetPasswordConfirmation = () => {
        setResetPasswordConfirmationOpen(true)
    }

    // Handle reset password action
    const handleConfirmResetPassword = async () => {
        await resetPassword(
            { userId: userData.id },
            {
                onSuccess: () => {
                    setResetPasswordConfirmationOpen(false)
                },
            },
        )
    }

    // Handle reset password cancellation
    const handleResetPasswordCancel = () => {
        setResetPasswordConfirmationOpen(false)
    }

    // Handle edit action
    const handleEdit = () => {
        if (onEdit) {
            onEdit(userData)
        }
    }

    // Handle delete confirmation
    const handleDeleteConfirmation = () => {
        setDeleteConfirmationOpen(true)
    }

    // Handle delete action
    const handleConfirmDelete = async () => {
        await deleteUser(userData.id)
        setDeleteConfirmationOpen(false)
    }

    // Handle delete cancellation
    const handleDeleteCancel = () => {
        setDeleteConfirmationOpen(false)
    }

    return (
        <>
            <div className="flex justify-center items-center gap-2 text-lg">
                <Tooltip
                    title={
                        t('nav.shared.resetPasswordConfirm') || 'Reset Password'
                    }
                >
                    <span
                        className="cursor-pointer hover:text-blue-500 transition-colors"
                        onClick={handleResetPasswordConfirmation}
                    >
                        <PiKeyholeDuotone />
                    </span>
                </Tooltip>
                <Tooltip title={t('nav.shared.edit') || 'Edit'}>
                    <span
                        className="cursor-pointer hover:text-blue-500 transition-colors"
                        onClick={handleEdit}
                    >
                        <HiOutlinePencil />
                    </span>
                </Tooltip>
                {userData.status !== 1 && (
                    <Tooltip title={t('nav.shared.delete') || 'Delete'}>
                        <span
                            className="cursor-pointer hover:text-blue-500 transition-colors"
                            onClick={handleDeleteConfirmation}
                        >
                            <HiOutlineTrash />
                        </span>
                    </Tooltip>
                )}
            </div>

            <ConfirmDialog
                isOpen={resetPasswordConfirmationOpen}
                type="warning"
                title={t('nav.users.resetPassword') || 'Reset Password'}
                onClose={handleResetPasswordCancel}
                onRequestClose={handleResetPasswordCancel}
                onCancel={handleResetPasswordCancel}
                onConfirm={handleConfirmResetPassword}
            >
                <p>
                    {t('nav.shared.confirmResetPassword') ||
                        `Are you sure you want to reset the password for user "${userData.fullName}"? This action will generate a new temporary password.`}
                </p>
            </ConfirmDialog>

            <ConfirmDialog
                isOpen={deleteConfirmationOpen}
                type="danger"
                title={t('nav.shared.deleteUser') || 'Delete User'}
                onClose={handleDeleteCancel}
                onRequestClose={handleDeleteCancel}
                onCancel={handleDeleteCancel}
                onConfirm={handleConfirmDelete}
            >
                <p>
                    {t('nav.shared.confirmDeleteUser') ||
                        `Are you sure you want to delete user "${userData.fullName}"? This action cannot be undone.`}
                </p>
            </ConfirmDialog>
        </>
    )
}
