import { useMutation, useQueryClient } from '@tanstack/react-query'
import { updateWarehouse } from '@/services/Warehouses'
import type { WarehouseForm, Warehouse } from '@/@types/warehouse'
import { showSuccessToast } from '@/utils/globalErrorHandler'
import { useErrorHandler } from '@/utils/errorHandler400'
import { AxiosError } from 'axios'

export const useUpdateWarehouse = () => {
    const queryClient = useQueryClient()
    const { handle400Errors } = useErrorHandler()

    return useMutation<
        Warehouse,
        AxiosError,
        { id: string; warehouse: WarehouseForm }
    >({
        mutationFn: ({ id, warehouse }) => updateWarehouse(id, warehouse),
        onSuccess: (_, { id }) => {
            queryClient.invalidateQueries({ queryKey: ['warehouses'] })
            queryClient.invalidateQueries({ queryKey: ['warehouse', id] })
            showSuccessToast('Warehouse updated successfully!')
        },
        onError: (error: AxiosError) => {
            handle400Errors(error, 'Failed to update warehouse')
        },
    })
}
