/* eslint-disable @typescript-eslint/no-explicit-any */
import { useMutation, useQueryClient } from '@tanstack/react-query'
import { updateDigitizationStation } from '@/services/DigitizationStationService'
import type { DigitizationStationRequest } from '@/@types/digitizationStation'
import { showSuccessToast } from '@/utils/globalErrorHandler'
import { useErrorHandler } from '@/utils/errorHandler400'
import { AxiosError } from 'axios'

export const useUpdateDigitizationStation = () => {
    const queryClient = useQueryClient()
    const { handle400Errors } = useErrorHandler()

    return useMutation<
        any,
        AxiosError,
        { id: string | number; station: DigitizationStationRequest }
    >({
        mutationFn: ({ id, station }) => updateDigitizationStation(id, station),
        onSuccess: (_, { id }) => {
            queryClient.invalidateQueries({
                queryKey: ['digitization-stations'],
            })
            queryClient.invalidateQueries({
                queryKey: ['digitization-station', id],
            })
            showSuccessToast('Digitization station updated successfully!')
        },
        onError: (error: AxiosError) => {
            handle400Errors(error, 'Failed to update digitization station')
        },
    })
}
