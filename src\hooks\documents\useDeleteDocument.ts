import { useMutation, useQueryClient } from '@tanstack/react-query'
import { deleteDocument } from '@/services/DocumentsService'
import { showSuccessToast } from '@/utils/globalErrorHandler'
import { useErrorHandler } from '@/utils/errorHandler400'
import { AxiosError } from 'axios'

export const useDeleteDocument = () => {
    const queryClient = useQueryClient()
    const { handle400Errors } = useErrorHandler()

    return useMutation({
        mutationFn: (documentId: string) => deleteDocument(documentId),
        onSuccess: (_, documentId) => {
            queryClient.invalidateQueries({
                queryKey: ['document', documentId],
            })
            queryClient.invalidateQueries({ queryKey: ['documents'] })
            showSuccessToast('Document deleted successfully!')
        },
        onError: (error: AxiosError) => {
            handle400Errors(error, 'Failed to delete document')
        },
    })
}
