import { deleteOrganizeStructure } from '@/services/OrganizeStructureService'
import { useMutation, useQueryClient } from '@tanstack/react-query'
import { showSuccessToast } from '@/utils/globalErrorHandler'
import { useErrorHandler } from '@/utils/errorHandler400'
import { AxiosError } from 'axios'

export const useDeleteOrgStructure = () => {
    const queryClient = useQueryClient()
    const { handle400Errors } = useErrorHandler()

    return useMutation<any, AxiosError, string>({
        mutationFn: (code: string) => deleteOrganizeStructure(code),
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: ['orgsStructure'] })
            queryClient.invalidateQueries({ queryKey: ['orgStructure'] })
            showSuccessToast('Organization structure deleted successfully!')
        },
        onError: (error: AxiosError) => {
            handle400Errors(error, 'Failed to delete organization structure')
        },
    })
}
