import {
    Document,
    DocumentDetails,
    DocumentCreate,
    DocumentUpdate,
} from '@/@types/document'
import ApiService from './ApiService'
import { Status } from '@/@types/common'
import { PaginationResponse } from '@/@types/global'

const BASE_ROUTE = '/Documents'

type DocumentsResponse = {
    items: Document[]
} & PaginationResponse

// GET /api/Documents/file/{fileId} - Get all documents belonging to a specific file
export async function getDocumentsByFile(
    fileId: string,
    {
        pageNumber = 1,
        pageSize = 10,
        status = undefined,
    }: {
        pageNumber?: number
        pageSize?: number
        status?: Status | undefined
    },
) {
    return ApiService.get<DocumentsResponse>(`${BASE_ROUTE}/file/${fileId}`, {
        params: { pageNumber, pageSize, status },
    })
}

// GET /api/Documents/{documentId} - Get document details by ID
export async function getDocumentById(documentId: string) {
    return ApiService.get<DocumentDetails>(`${BASE_ROUTE}/${documentId}`)
}

// POST /api/Documents - Create a new document within a file
export async function createDocument(document: DocumentCreate) {
    return ApiService.post(`${BASE_ROUTE}`, document)
}

// PUT /api/Documents/{documentId} - Update document with PDF upload and metadata
export async function updateDocument(
    documentId: string,
    document: DocumentUpdate,
) {
    return ApiService.put<void, DocumentUpdate>(
        `${BASE_ROUTE}/${documentId}`,
        document,
    )
}

// POST /api/Documents/{documentId}/upload-document - Upload a PDF file to an existing document
export async function uploadDocumentPdfApi(documentId: string, pdfFile: File) {
    const formData = new FormData()
    formData.append('PdfFile', pdfFile)
    return ApiService.post<void>(
        `${BASE_ROUTE}/${documentId}/upload-document`,
        formData as unknown as Record<string, unknown>,
        {
            headers: {
                'Content-Type': 'multipart/form-data',
            },
        },
    )
}

// POST /api/Documents/{documentId}/upload-attachments - Upload attachment files to an existing document
export async function uploadDocumentAttachmentsApi(
    documentId: string,
    attachments: File[],
) {
    const formData = new FormData()
    attachments.forEach((file) => {
        formData.append('Attachments', file)
    })
    return ApiService.post<void>(
        `${BASE_ROUTE}/${documentId}/upload-attachments`,
        formData as unknown as Record<string, unknown>,
        {
            headers: {
                'Content-Type': 'multipart/form-data',
            },
        },
    )
}

// PATCH /api/Documents/{documentId}/status/digitized - Change document status to Digitized
export async function markDocumentAsDigitizedApi(documentId: string) {
    return ApiService.patch<void>(
        `${BASE_ROUTE}/${documentId}/status/digitized`,
    )
}

// DELETE /api/Documents/{documentId} - Delete a document and its associated files
export async function deleteDocument(documentId: string) {
    return ApiService.delete<void>(`${BASE_ROUTE}/${documentId}`)
}

// GET /api/Documents/view - Downloads/views a file by its relative path
export async function viewDocument(filePath: string) {
    return ApiService.get<Blob>(`${BASE_ROUTE}/view/${filePath}`, {
        responseType: 'blob',
    })
}
