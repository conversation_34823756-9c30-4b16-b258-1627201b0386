import { useMutation, useQueryClient } from '@tanstack/react-query'
import { deleteDigitizationStation } from '@/services/DigitizationStationService'
import { showSuccessToast } from '@/utils/globalErrorHandler'
import { useErrorHandler } from '@/utils/errorHandler400'
import { AxiosError } from 'axios'

export const useDeleteDigitizationStation = () => {
    const queryClient = useQueryClient()
    const { handle400Errors } = useErrorHandler()

    return useMutation<void, AxiosError, string | number>({
        mutationFn: deleteDigitizationStation,
        onSuccess: () => {
            queryClient.invalidateQueries({
                queryKey: ['digitization-stations'],
            })
            showSuccessToast('Digitization station deleted successfully!')
        },
        onError: (error: AxiosError) => {
            handle400Errors(error, 'Failed to delete digitization station')
        },
    })
}
