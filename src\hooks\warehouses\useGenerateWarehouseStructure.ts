/* eslint-disable @typescript-eslint/no-explicit-any */
import { useMutation, useQueryClient } from '@tanstack/react-query'
import { generateWarehouseStructure } from '@/services/Warehouses'
import type { WarehouseStructure } from '@/@types/warehouse'
import { showSuccessToast } from '@/utils/globalErrorHandler'
import { useErrorHandler } from '@/utils/errorHandler400'
import { AxiosError } from 'axios'

export const useGenerateWarehouseStructure = () => {
    const queryClient = useQueryClient()
    const { handle400Errors } = useErrorHandler()

    return useMutation<
        any,
        AxiosError,
        { id: string; structure: WarehouseStructure }
    >({
        mutationFn: ({ id, structure }) =>
            generateWarehouseStructure(id, structure),
        onSuccess: (_, { id }) => {
            queryClient.invalidateQueries({ queryKey: ['warehouse', id] })
            showSuccessToast('Warehouse structure generated successfully!')
        },
        onError: (error: AxiosError) => {
            handle400Errors(error, 'Failed to generate warehouse structure')
        },
    })
}
