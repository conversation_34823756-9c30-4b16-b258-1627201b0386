import { useMutation, useQueryClient } from '@tanstack/react-query'
import { updateKeywordApi } from '@/services/Keywords'
import { showSuccessToast } from '@/utils/globalErrorHandler'
import { useErrorHandler } from '@/utils/errorHandler400'
import { AxiosError } from 'axios'

export const useUpdateKeyword = () => {
    const queryClient = useQueryClient()
    const { handle400Errors } = useErrorHandler()

    return useMutation<any, AxiosError, { id: number; value: string }>({
        mutationFn: ({ id, value }: { id: number; value: string }) =>
            updateKeywordApi(id, value),
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: ['keywords'] })
            showSuccessToast('Keyword updated successfully!')
        },
        onError: (error: AxiosError) => {
            handle400Errors(error, 'Failed to update keyword')
        },
    })
}

export default useUpdateKeyword
