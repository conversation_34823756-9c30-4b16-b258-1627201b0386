import { useMutation, useQueryClient } from '@tanstack/react-query'
import { deleteMultipleGeneralCategories } from '@/services/GeneralCategoriesService'
import { showSuccessToast } from '@/utils/globalErrorHandler'
import { useErrorHandler } from '@/utils/errorHandler400'
import { AxiosError } from 'axios'

export const useDeleteMultipleGeneralCategories = () => {
    const queryClient = useQueryClient()
    const { handle400Errors } = useErrorHandler()

    return useMutation<any, AxiosError, number[]>({
        mutationFn: deleteMultipleGeneralCategories,
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: ['generalCategories'] })
            showSuccessToast('General categories deleted successfully!')
        },
        onError: (error: AxiosError) => {
            handle400Errors(error, 'Failed to delete general categories')
        },
    })
}

export default useDeleteMultipleGeneralCategories
