import { useMutation, useQueryClient } from '@tanstack/react-query'
import { updateDocument } from '@/services/DocumentsService'
import { DocumentUpdate } from '@/@types/document'
import { showSuccessToast } from '@/utils/globalErrorHandler'
import { useErrorHandler } from '@/utils/errorHandler400'
import { AxiosError } from 'axios'

export const useUpdateDocument = () => {
    const queryClient = useQueryClient()
    const { handle400Errors } = useErrorHandler()

    return useMutation<
        void,
        AxiosError,
        { documentId: string; document: DocumentUpdate }
    >({
        mutationFn: ({
            documentId,
            document,
        }: {
            documentId: string
            document: DocumentUpdate
        }) => updateDocument(documentId, document),
        onSuccess: (_, documentId) => {
            queryClient.invalidateQueries({
                queryKey: ['document', documentId],
            })
            queryClient.invalidateQueries({ queryKey: ['documents'] })
            showSuccessToast('Document updated successfully!')
        },
        onError: (error: AxiosError) => {
            handle400Errors(error, 'Failed to update document')
        },
    })
}
