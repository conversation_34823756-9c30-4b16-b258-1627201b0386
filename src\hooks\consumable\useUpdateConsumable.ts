import { useMutation, useQueryClient } from '@tanstack/react-query'
import { updateConsumable } from '@/services/ConsumableService'
import type { ConsumableUpdata } from '@/@types/consumable'
import { showSuccessToast } from '@/utils/globalErrorHandler'
import { useErrorHandler } from '@/utils/errorHandler400'
import { AxiosError } from 'axios'

export const useUpdateConsumable = () => {
    const queryClient = useQueryClient()
    const { handle400Errors } = useErrorHandler()

    return useMutation<
        any,
        AxiosError,
        { id: number; consumable: ConsumableUpdata }
    >({
        mutationFn: ({
            id,
            consumable,
        }: {
            id: number
            consumable: ConsumableUpdata
        }) => updateConsumable(id, consumable),
        onSuccess: (data, variables) => {
            queryClient.invalidateQueries({ queryKey: ['consumables'] })
            queryClient.invalidateQueries({
                queryKey: ['consumable', variables.id],
            })
            showSuccessToast('Consumable updated successfully!')
        },
        onError: (error: AxiosError) => {
            handle400Errors(error, 'Failed to update consumable')
        },
    })
}
