import { useMutation, useQueryClient } from '@tanstack/react-query'
import { createStock } from '@/services/StockService'
import type { Stock, StockRequest } from '@/@types/stocks'
import { showSuccessToast } from '@/utils/globalErrorHandler'
import { useErrorHandler } from '@/utils/errorHandler400'
import { AxiosError } from 'axios'

export const useCreateStock = () => {
    const queryClient = useQueryClient()
    const { handle400Errors } = useErrorHandler()

    return useMutation<Stock, AxiosError, StockRequest>({
        mutationFn: createStock,
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: ['stocks'] })
            showSuccessToast('Stock created successfully!')
        },
        onError: (error: AxiosError) => {
            handle400Errors(error, 'Failed to create stock')
        },
    })
}
