import { useMutation, useQueryClient } from '@tanstack/react-query'
import { changeStatusBuilding } from '@/services/BuildingService'
import { Status } from '@/@types/common'
import { showSuccessToast } from '@/utils/globalErrorHandler'
import { useErrorHandler } from '@/utils/errorHandler400'
import { AxiosError } from 'axios'

export const useChangeStatusBuilding = () => {
    const queryClient = useQueryClient()
    const { handle400Errors } = useErrorHandler()

    return useMutation<any, AxiosError, { id: number; status: Status }>({
        mutationFn: ({ id, status }: { id: number; status: Status }) =>
            changeStatusBuilding(id, status),
        onSuccess: (_, { id }) => {
            queryClient.invalidateQueries({ queryKey: ['buildings'] })
            queryClient.invalidateQueries({ queryKey: ['building', id] })
            showSuccessToast('Building status updated successfully!')
        },
        onError: (error: AxiosError) => {
            handle400Errors(error, 'Failed to update building status')
        },
    })
}
