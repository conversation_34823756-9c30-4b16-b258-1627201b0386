import { updateOrganizeStructure } from '@/services/OrganizeStructureService'
import { useMutation, useQueryClient } from '@tanstack/react-query'
import type { UpdatedNode } from '@/@types/organizationalStructure'
import { showSuccessToast } from '@/utils/globalErrorHandler'
import { useError<PERSON>andler } from '@/utils/errorHandler400'
import { AxiosError } from 'axios'

export const useUpdateOrgStructure = () => {
    const queryClient = useQueryClient()
    const { handle400Errors } = useErrorHandler()

    return useMutation<any, AxiosError, { code: string; data: UpdatedNode }>({
        mutationFn: ({ code, data }: { code: string; data: UpdatedNode }) =>
            updateOrganizeStructure(code, data),
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: ['orgsStructure'] })
            queryClient.invalidateQueries({ queryKey: ['orgStructure'] })
            showSuccessToast('Organization structure updated successfully!')
        },
        onError: (error: AxiosError) => {
            handle400Errors(error, 'Failed to update organization structure')
        },
    })
}
