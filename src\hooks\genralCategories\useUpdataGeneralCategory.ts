import { useMutation, useQueryClient } from '@tanstack/react-query'
import { updateGeneralCategory } from '@/services/GeneralCategoriesService'
import { showSuccessToast } from '@/utils/globalErrorHandler'
import { useErrorHandler } from '@/utils/errorHandler400'
import { AxiosError } from 'axios'

export const useUpdateGeneralCategory = () => {
    const queryClient = useQueryClient()
    const { handle400Errors } = useErrorHandler()

    return useMutation<any, AxiosError, { id: number; name: string }>({
        mutationFn: (variables: { id: number; name: string }) =>
            updateGeneralCategory(variables.id, variables.name),
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: ['generalCategories'] })
            showSuccessToast('General category updated successfully!')
        },
        onError: (error: AxiosError) => {
            handle400Errors(error, 'Failed to update general category')
        },
    })
}
