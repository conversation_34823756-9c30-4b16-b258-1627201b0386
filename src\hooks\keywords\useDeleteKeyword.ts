import { useMutation, useQueryClient } from '@tanstack/react-query'
import { deleteKeywordApi } from '@/services/Keywords'
import { showSuccessToast } from '@/utils/globalErrorHandler'
import { useErrorHandler } from '@/utils/errorHandler400'
import { AxiosError } from 'axios'

export const useDeleteKeyword = () => {
    const queryClient = useQueryClient()
    const { handle400Errors } = useErrorHandler()

    return useMutation<any, AxiosError, number>({
        mutationFn: deleteKeywordApi,
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: ['keywords'] })
            showSuccessToast('Keyword deleted successfully!')
        },
        onError: (error: AxiosError) => {
            handle400Errors(error, 'Failed to delete keyword')
        },
    })
}

export default useDeleteKeyword
