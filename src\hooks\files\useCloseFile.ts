import { useMutation, useQueryClient } from '@tanstack/react-query'
import { closeFile } from '@/services/FilesService'
import { showSuccessToast } from '@/utils/globalErrorHandler'
import { useErrorHandler } from '@/utils/errorHandler400'
import { AxiosError } from 'axios'

export const useCloseFile = () => {
    const queryClient = useQueryClient()
    const { handle400Errors } = useErrorHandler()

    return useMutation<any, AxiosError, any>({
        mutationFn: closeFile,
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: ['files'] })
            showSuccessToast('File closed successfully!')
        },
        onError: (error: AxiosError) => {
            handle400Errors(error, 'Failed to close file')
        },
    })
}
