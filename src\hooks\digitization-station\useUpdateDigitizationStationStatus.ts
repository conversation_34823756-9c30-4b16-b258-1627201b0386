import { useMutation, useQueryClient } from '@tanstack/react-query'
import { updateDigitizationStationStatus } from '@/services/DigitizationStationService'
import type { Status } from '@/@types/common'
import { showSuccessToast } from '@/utils/globalErrorHandler'
import { useErrorHandler } from '@/utils/errorHandler400'
import { AxiosError } from 'axios'

export const useUpdateDigitizationStationStatus = () => {
    const queryClient = useQueryClient()
    const { handle400Errors } = useErrorHandler()

    return useMutation<
        void,
        AxiosError,
        { id: string | number; status: Status }
    >({
        mutationFn: ({ id, status }) =>
            updateDigitizationStationStatus(id, status),
        onSuccess: (_, { id }) => {
            queryClient.invalidateQueries({
                queryKey: ['digitization-stations'],
            })
            queryClient.invalidateQueries({
                queryKey: ['digitization-station', id],
            })
            showSuccessToast(
                'Digitization station status updated successfully!',
            )
        },
        onError: (error: AxiosError) => {
            handle400Errors(
                error,
                'Failed to update digitization station status',
            )
        },
    })
}
