import { useMutation, useQueryClient } from '@tanstack/react-query'
import { createKeywordApi } from '@/services/Keywords'
import { showSuccessToast } from '@/utils/globalErrorHandler'
import { useErrorHandler } from '@/utils/errorHandler400'
import { AxiosError } from 'axios'

export const useCreateKeyword = () => {
    const queryClient = useQueryClient()
    const { handle400Errors } = useErrorHandler()

    return useMutation<any, AxiosError, string>({
        mutationFn: createKeywordApi,
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: ['keywords'] })
            showSuccessToast('Keyword created successfully!')
        },
        onError: (error: AxiosError) => {
            handle400Errors(error, 'Failed to create keyword')
        },
    })
}

export default useCreateKeyword
