import { useMutation, useQueryClient } from '@tanstack/react-query'
import type { CreateUser } from '@/@types/auth'
import { createNewUserApi } from '@/services/UsersService'
import { showSuccessToast } from '@/utils/globalErrorHandler'
import { useErrorHandler } from '@/utils/errorHandler400'
import { AxiosError } from 'axios'

export const useCreateUser = () => {
    const queryClient = useQueryClient()
    const { handle400Errors } = useErrorHandler()

    return useMutation<void, AxiosError, { data: CreateUser }>({
        mutationFn: ({ data }: { data: CreateUser }) => createNewUserApi(data),
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: ['users'] })
            showSuccessToast('User created successfully!')
        },
        onError: (error: AxiosError) => {
            handle400Errors(error, 'Failed to create user')
        },
    })
}
