/* eslint-disable @typescript-eslint/no-explicit-any */
import { useMutation, useQueryClient } from '@tanstack/react-query'
import { deleteCategory } from '@/services/CategoriesService'
import { showSuccessToast } from '@/utils/globalErrorHandler'
import { useErrorHandler } from '@/utils/errorHandler400'
import { AxiosError } from 'axios'

export const useDeleteCategory = () => {
    const queryClient = useQueryClient()
    const { handle400Errors } = useErrorHandler()

    return useMutation<any, AxiosError, string | number>({
        mutationFn: deleteCategory,
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: ['categories'] })
            showSuccessToast('Category deleted successfully!')
        },
        onError: (error: AxiosError) => {
            handle400Errors(error, 'Failed to delete category')
        },
    })
}
