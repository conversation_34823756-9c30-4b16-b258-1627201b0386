import { useMutation, useQueryClient } from '@tanstack/react-query'
import { updateConsumableStatus } from '@/services/ConsumableService'
import type { Status } from '@/@types/common'
import { showSuccessToast } from '@/utils/globalErrorHandler'
import { useError<PERSON>andler } from '@/utils/errorHandler400'
import { AxiosError } from 'axios'

export const useUpdateConsumableStatus = () => {
    const queryClient = useQueryClient()
    const { handle400Errors } = useErrorHandler()

    return useMutation<any, AxiosError, { id: number; status: Status }>({
        mutationFn: ({ id, status }: { id: number; status: Status }) =>
            updateConsumableStatus(id, status),
        onSuccess: (data, variables) => {
            queryClient.invalidateQueries({ queryKey: ['consumables'] })
            queryClient.invalidateQueries({
                queryKey: ['consumable', variables.id],
            })
            showSuccessToast('Consumable status updated successfully!')
        },
        onError: (error: AxiosError) => {
            handle400Errors(error, 'Failed to update consumable status')
        },
    })
}
