import { useMutation, useQueryClient } from '@tanstack/react-query'
import { createDocument } from '@/services/DocumentsService'
import { DocumentCreate } from '@/@types/document'
import { showSuccessToast } from '@/utils/globalErrorHandler'
import { useErrorHandler } from '@/utils/errorHandler400'
import { AxiosError } from 'axios'

export const useCreateDocument = () => {
    const queryClient = useQueryClient()
    const { handle400Errors } = useErrorHandler()

    return useMutation({
        mutationFn: ({ document }: { document: DocumentCreate }) =>
            createDocument(document),
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: ['documents'] })
            showSuccessToast('Document created successfully!')
        },
        onError: (error: AxiosError) => {
            handle400Errors(error, 'Failed to create document')
        },
    })
}
