import { useMutation, useQueryClient } from '@tanstack/react-query'
import { updateBuilding } from '@/services/BuildingService'
import { BuildingForm } from '@/@types/building'
import { showSuccessToast } from '@/utils/globalErrorHandler'
import { useErrorHandler } from '@/utils/errorHandler400'
import { AxiosError } from 'axios'

export const useUpdateBuilding = () => {
    const queryClient = useQueryClient()
    const { handle400Errors } = useErrorHandler()

    return useMutation<any, AxiosError, { id: number; building: BuildingForm }>(
        {
            mutationFn: ({
                id,
                building,
            }: {
                id: number
                building: BuildingForm
            }) => updateBuilding(id, building),
            onSuccess: (_, { id }) => {
                queryClient.invalidateQueries({ queryKey: ['buildings'] })
                queryClient.invalidateQueries({ queryKey: ['building', id] })
                showSuccessToast('Building updated successfully!')
            },
            onError: (error: AxiosError) => {
                handle400Errors(error, 'Failed to update building')
            },
        },
    )
}
