import { useMutation, useQueryClient } from '@tanstack/react-query'
import { createDocumentType } from '@/services/DocumentTypesService'
import { showSuccessToast } from '@/utils/globalErrorHandler'
import { useErrorHandler } from '@/utils/errorHandler400'
import { AxiosError } from 'axios'

export const useCreateDocumentType = () => {
    const queryClient = useQueryClient()
    const { handle400Errors } = useErrorHandler()

    return useMutation<any, AxiosError, string>({
        mutationFn: createDocumentType,
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: ['document-types'] })
            showSuccessToast('Document type created successfully!')
        },
        onError: (error: AxiosError) => {
            handle400Errors(error, 'Failed to create document type')
        },
    })
}
