/* eslint-disable @typescript-eslint/no-explicit-any */
import { useMutation, useQueryClient } from '@tanstack/react-query'
import { updateCategoryStatus } from '@/services/CategoriesService'
import { showSuccessToast } from '@/utils/globalErrorHandler'
import { useErrorHandler } from '@/utils/errorHandler400'
import { AxiosError } from 'axios'

export const useUpdateCategoryStatus = () => {
    const queryClient = useQueryClient()
    const { handle400Errors } = useErrorHandler()

    return useMutation<
        any,
        AxiosError,
        { id: string | number; status: number }
    >({
        mutationFn: ({ id, status }) => updateCategoryStatus(id, status),
        onSuccess: (_, { id }) => {
            queryClient.invalidateQueries({ queryKey: ['categories'] })
            queryClient.invalidateQueries({ queryKey: ['category', id] })
            showSuccessToast('Category status updated successfully!')
        },
        onError: (error: AxiosError) => {
            handle400Errors(error, 'Failed to update category status')
        },
    })
}
