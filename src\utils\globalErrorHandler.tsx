import { toast, Notification } from '@/components/ui'
import type { AxiosError } from 'axios'
import { useTranslation } from 'react-i18next'

export const handleGlobalError = (error: AxiosError) => {
    const status = error.response?.status

    if (status === 400) {
        return
    }

    // Handle network errors
    if (!status && error.code === 'NETWORK_ERROR') {
        showErrorToast('errors.networkError')
        return
    }

    // Handle specific HTTP status codes
    const statusToHandle = [
        401, 403, 404, 408, 409, 422, 429, 500, 502, 503, 504,
    ]

    if (status && statusToHandle.includes(status)) {
        const errorKey = `errors.${status}`
        showErrorToast(errorKey)
    } else if (status) {
        // For any other status codes, show unknown error
        showErrorToast('errors.unknownError')
    }
}

/**
 * Show error toast with translation
 */
const showErrorToast = (translationKey: string) => {
    const { t } = useTranslation()

    toast.push(
        <Notification title="" type="danger" duration={4000}>
            {t(translationKey)}
        </Notification>,
        { placement: 'top-center' },
    )
}

/**
 * Check if error should be handled globally
 */
export const shouldHandleGlobally = (status?: number): boolean => {
    // Don't handle 400 errors globally
    if (status === 400) {
        return false
    }

    // Handle all other error status codes globally
    const globalStatusCodes = [
        401, 403, 404, 408, 409, 422, 429, 500, 502, 503, 504,
    ]
    return !status || globalStatusCodes.includes(status)
}

/**
 * Show custom error toast for 400 errors or other manual error handling
 * This can be used by components to show custom error messages
 */
export const showCustomErrorToast = (
    message: string,
    duration: number = 3000,
) => {
    toast.push(
        <Notification title="" type="danger" duration={duration}>
            {message}
        </Notification>,
        { placement: 'top-center' },
    )
}

/**
 * Show success toast
 */
export const showSuccessToast = (message: string, duration: number = 3000) => {
    toast.push(
        <Notification title="" type="success" duration={duration}>
            {message}
        </Notification>,
        { placement: 'top-center' },
    )
}
