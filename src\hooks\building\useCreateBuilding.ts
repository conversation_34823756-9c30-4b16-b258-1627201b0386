import { useMutation, useQueryClient } from '@tanstack/react-query'
import { createBuilding } from '@/services/BuildingService'
import { BuildingForm } from '@/@types/building'
import { showSuccessToast } from '@/utils/globalErrorHandler'
import { useErrorHandler } from '@/utils/errorHandler400'
import { AxiosError } from 'axios'

export const useCreateBuilding = () => {
    const queryClient = useQueryClient()
    const { handle400Errors } = useErrorHandler()

    return useMutation<any, AxiosError, BuildingForm>({
        mutationFn: (building: BuildingForm) => createBuilding(building),
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: ['buildings'] })
            showSuccessToast('Building created successfully!')
        },
        onError: (error: AxiosError) => {
            handle400Errors(error, 'Failed to create building')
        },
    })
}
