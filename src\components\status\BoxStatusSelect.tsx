import Select from '@/components/ui/Select'
import useTranslation from '@/utils/hooks/useTranslation'
import { getBoxStatusOptions } from '@/utils/status'

interface BoxStatusSelectProps {
    value: string
    onChange: (value: string) => void
    placeholder?: string
    includeAll?: boolean
    className?: string
    isDisabled?: boolean
    menuPlacement?: 'auto' | 'bottom' | 'top'
    allowedStatuses?: number[]
}

const BoxStatusSelect = ({
    value,
    onChange,
    placeholder,
    includeAll = false,
    className = '',
    isDisabled = false,
    menuPlacement = 'auto',
    allowedStatuses,
}: BoxStatusSelectProps) => {
    const { t } = useTranslation()

    // Get status options using unified configuration
    const statusOptions = getBoxStatusOptions(t, {
        includeAll,
        allowedStatuses,
    })

    // Find the selected option
    const selectedOption = statusOptions.find(
        (option) => option.value === value,
    )

    const handleChange = (
        selectedOption: { value: string; label: string } | null,
    ) => {
        onChange(selectedOption?.value || '')
    }

    return (
        <Select
            value={selectedOption}
            options={statusOptions}
            placeholder={placeholder || t('nav.shared.selectStatus')}
            className={className}
            isDisabled={isDisabled}
            menuPlacement={menuPlacement}
            onChange={handleChange}
        />
    )
}

export default BoxStatusSelect
