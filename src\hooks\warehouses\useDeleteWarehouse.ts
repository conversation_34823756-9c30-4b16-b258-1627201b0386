import { useMutation, useQueryClient } from '@tanstack/react-query'
import { deleteWarehouse } from '@/services/Warehouses'
import { showSuccessToast } from '@/utils/globalErrorHandler'
import { useErrorHandler } from '@/utils/errorHandler400'
import { AxiosError } from 'axios'

export const useDeleteWarehouse = () => {
    const queryClient = useQueryClient()
    const { handle400Errors } = useErrorHandler()

    return useMutation<void, AxiosError, string>({
        mutationFn: deleteWarehouse,
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: ['warehouses'] })
            showSuccessToast('Warehouse deleted successfully!')
        },
        onError: (error: AxiosError) => {
            handle400Errors(error, 'Failed to delete warehouse')
        },
    })
}
