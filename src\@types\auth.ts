import { Status } from './common'

export type SignInCredential = {
    email: string
    password: string
}

interface UserData {
    id: string
    email: string
    firstName: string
    lastName: string
}

export type SignInResponse = {
    user: UserData
    token: string
    expiresIn?: number
    refreshToken: string
    refreshTokenExpiration?: string
    mustChangePassword: boolean
    authority?: string[]
}

export type AuthResult = Promise<{
    status: string
    message: string
    code?: string
    description?: string
    headers?: Record<string, unknown>
}>

export type OrganizationalNode = {
    code: string
    name: string
}

export interface User {
    id: string
    firstName: string
    lastName: string
    fullName: string
    email: string
    phoneNumber: string
    profilePictureUrl: string | null
    status: Status
    mustChangePassword?: boolean
    organizationalNodes: OrganizationalNode[]
}

export interface CreateUser {
    firstName: string
    lastName: string
    email: string
    phoneNumber: string
    organizationalNodeCodes: string[]
}

export type Token = {
    accessToken?: string | null
    refreshToken?: string | null
}

export type Branch = {
    code: string
    name: string
}

export type BranchSelectionState = {
    selectedBranch: Branch | null
    availableBranches: Branch[]
    isSelecting: boolean
    hasSelectedBranch: boolean
    showDialog: boolean
    showConfirmation: boolean
}
