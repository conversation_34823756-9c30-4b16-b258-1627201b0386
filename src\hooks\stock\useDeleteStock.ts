import { useMutation, useQueryClient } from '@tanstack/react-query'
import { deleteStock } from '@/services/StockService'
import { showSuccessToast } from '@/utils/globalErrorHandler'
import { useErrorHandler } from '@/utils/errorHandler400'
import { AxiosError } from 'axios'

export const useDeleteStock = () => {
    const queryClient = useQueryClient()
    const { handle400Errors } = useErrorHandler()

    return useMutation<void, AxiosError, number>({
        mutationFn: deleteStock,
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: ['stocks'] })
            showSuccessToast('Stock deleted successfully!')
        },
        onError: (error: AxiosError) => {
            handle400Errors(error, 'Failed to delete stock')
        },
    })
}
