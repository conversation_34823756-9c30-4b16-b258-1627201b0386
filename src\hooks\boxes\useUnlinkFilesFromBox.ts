import { useMutation, useQueryClient } from '@tanstack/react-query'
import { unlinkFilesFromBox } from '@/services/Boxes'
import { useErrorHandler } from '@/utils/errorHandler400'
import { AxiosError } from 'axios'

export const useUnlinkFilesFromBox = () => {
    const queryClient = useQueryClient()
    const { handle400Errors } = useErrorHandler()

    return useMutation<
        void,
        Error,
        { boxId: string | number; fileIds: string[] }
    >({
        mutationFn: ({ boxId, fileIds }) => unlinkFilesFromBox(boxId, fileIds),
        onSuccess: (_, variables) => {
            queryClient.invalidateQueries({ queryKey: ['boxes'] })
            queryClient.invalidateQueries({
                queryKey: ['box', variables.boxId],
            })
            queryClient.invalidateQueries({ queryKey: ['files'] })
        },
        onError: (error) => {
            handle400Errors(
                error as AxiosError,
                'Failed to unlink files from box',
            )
        },
    })
}
