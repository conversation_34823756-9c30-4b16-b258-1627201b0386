/* eslint-disable @typescript-eslint/no-explicit-any */
import React, { useMemo, useState } from 'react'
import { DataTable } from '@/components/shared'
import useTranslation from '@/utils/hooks/useTranslation'
import { User } from '@/@types/auth'
import Status from '@/components/shared/displaying/Status'
import { useGetUsers } from '@/hooks/user'
import Actions from './Actions'

interface UsersTableProps {
    onEdit?: (user: User) => void
}

const UsersTable = ({ onEdit }: UsersTableProps) => {
    const { data: users = [], isLoading } = useGetUsers()

    const { t } = useTranslation()

    const [tableData, setTableData] = useState({
        pageIndex: 1,
        pageSize: 10,
        sort: {
            order: '',
            key: '',
        },
        query: '',
        total: 0,
    })

    const [selectedUserId, setSelectedUserId] = useState<string | null>(null)

    // Filter and sort users based on table data
    const filteredUsers = useMemo(() => {
        let filtered = [...users]

        // Apply search filter
        if (tableData.query) {
            filtered = filtered.filter(
                (user) =>
                    user.fullName
                        ?.toLowerCase()
                        .includes(tableData.query.toLowerCase()) ||
                    user.email
                        ?.toLowerCase()
                        .includes(tableData.query.toLowerCase()),
            )
        }

        // Apply sorting
        if (tableData.sort.key && tableData.sort.order) {
            filtered.sort((a, b) => {
                const aValue = (a as any)[tableData.sort.key]
                const bValue = (b as any)[tableData.sort.key]

                if (tableData.sort.order === 'asc') {
                    return aValue > bValue ? 1 : -1
                } else {
                    return aValue < bValue ? 1 : -1
                }
            })
        }

        return filtered
    }, [users, tableData.query, tableData.sort])

    // Get paginated data for current page
    const paginatedUsers = useMemo(() => {
        const startIndex = (tableData.pageIndex - 1) * tableData.pageSize
        const endIndex = startIndex + tableData.pageSize
        return filteredUsers.slice(startIndex, endIndex)
    }, [filteredUsers, tableData.pageIndex, tableData.pageSize])

    // Handle pagination changes
    const handlePaginationChange = (pageIndex: number) => {
        setTableData((prevData) => ({ ...prevData, pageIndex }))
    }

    // Handle sort changes
    const handleSort = (sort: { order: string; key: string }) => {
        setTableData((prevData) => ({ ...prevData, sort, pageIndex: 1 })) // Reset to first page when sorting
    }

    // Handle search changes
    // const handleSearch = (query: string) => {
    //     setTableData((prevData) => ({ ...prevData, query, pageIndex: 1 })) // Reset to first page when searching
    // }

    // Handle row selection
    const handleRowSelect = (checked: boolean, row: User) => {
        if (checked) {
            setSelectedUserId(row.id)
        } else {
            setSelectedUserId(null)
        }
    }

    // Define table columns
    const columns = useMemo(
        () => [
            {
                header: '#',
                accessorKey: 'profilePictureUrl',
                cell: (props: any) => {
                    const row = props.row.original
                    return (
                        <div className="w-full flex justify-center ">
                            <span className="w-8 h-8 rounded-full">
                                <img
                                    src={
                                        row.profilePictureUrl ||
                                        `https://randomuser.me/api/portraits/men/${Math.floor(Math.random() * 100) + 1}.jpg`
                                    }
                                    alt={row.fullName}
                                    className="w-8 h-8 rounded-full"
                                />
                            </span>
                        </div>
                    )
                },
                enableSorting: false,
            },
            {
                header: t('nav.shared.name') || 'Name',
                accessorKey: 'fullName',
                cell: (props: any) => {
                    const row = props.row.original
                    return <span className="font-semibold">{row.fullName}</span>
                },
            },
            {
                header: t('nav.shared.email'),
                accessorKey: 'email',
                cell: (props: any) => {
                    const row = props.row.original
                    return (
                        <span className="text-sm text-gray-600 dark:text-gray-400">
                            {row.email || '-'}
                        </span>
                    )
                },
                enableSorting: false,
            },
            {
                header: t('nav.shared.phoneNumber'),
                accessorKey: 'phoneNumber',
                cell: (props: any) => {
                    const row = props.row.original
                    return (
                        <span className="text-sm text-gray-600 dark:text-gray-400">
                            {row.phoneNumber || '-'}
                        </span>
                    )
                },
                enableSorting: false,
            },
            {
                header: t('nav.shared.status') || 'Status',
                accessorKey: 'status',
                cell: (props: any) => <Status row={props.row} />,
                enableSorting: false,
            },
            {
                header: t('nav.shared.actions') || 'Actions',
                id: 'actions',
                cell: (props: any) => {
                    const row = props.row.original
                    return (
                        <Actions
                            userData={row}
                            onEdit={onEdit}
                        />
                    )
                },
            },
        ],
        [t],
    )

    // Filter columns based on visibleColumns prop
    const visibleColumnsData = useMemo(() => {
        return columns
    }, [columns])

    return (
        <>
            <DataTable
                columns={visibleColumnsData}
                data={paginatedUsers}
                noData={!isLoading && filteredUsers.length === 0}
                skeletonAvatarColumns={[0]}
                skeletonAvatarProps={{ width: 28, height: 28 }}
                loading={isLoading}
                pagingData={{
                    total: filteredUsers.length,
                    pageIndex: tableData.pageIndex as number,
                    pageSize: tableData.pageSize as number,
                }}
                checkboxChecked={(row) => selectedUserId === row.id}
                cellBorder={true}
                onPaginationChange={handlePaginationChange}
                onSort={(sort) =>
                    handleSort({ order: sort.order, key: String(sort.key) })
                }
                onCheckBoxChange={handleRowSelect}
            />
        </>
    )
}

export default UsersTable
